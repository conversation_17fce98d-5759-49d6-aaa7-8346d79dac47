<template>
    <!-- Main Start -->
    <!-- 头部修改头像 -->
    <view class="header bg-white py-[30rpx]">
        <view class="flex">
            <button class="flex flex-col items-center after:border-0" hover-class="none" open-type="chooseAvatar"
                style="background-color: transparent" @click="chooseAvatar" @chooseavatar="chooseAvatar">
                <image :src="getImageUrl(userInfo?.avatar)"></image>
                <view class="mt-[10rpx] text-center text-muted text-xs"> 点击修改头像 </view>
            </button>
        </view>
    </view>

    <!-- 用户ID -->
    <view class="flex justify-between item text-nr">
        <view class="label">账号</view>
        <view class="content">{{ userInfo?.username }}</view>
    </view>

    <!-- 昵称 -->
    <view class="flex justify-between item text-nr" @click="; (showNickName = true), (newNickname = userInfo?.nickname)">
        <view class="label">昵称</view>
        <view class="content">{{ userInfo?.nickname }}</view>
        <u-icon name="arrow-right" size="22" color="#666"></u-icon>
    </view>

    <!-- 手机号 -->
    <view class="flex justify-between item text-nr">
        <view class="label">手机号</view>
        <view class="content">{{ userInfo?.phone ? userInfo?.phone : '未绑定手机号' }}</view>

        <u-button @click="showMobilePop = true" size="mini" type="primary" shape="circle" :plain="true">
            {{ userInfo?.phone == '' ? '绑定手机号' : '更换手机号' }}
        </u-button>
    </view>

    <!-- 注册时间 -->
    <view class="flex justify-between item text-nr">
        <view class="label">注册时间</view>
        <view class="content">{{ userInfo?.createTime }}</view>
    </view>

    <!-- 昵称修改组件 -->
    <u-popup v-model="showNickName" :closeable="true" mode="center" :maskCloseAble="false" border-radius="20">
        <view class="px-[50rpx] py-[40rpx] bg-white" style="width: 85vw">
            <form @submit="changeNameConfirm">
                <view class="mb-[70rpx] text-xl text-center">修改昵称</view>
                <u-form-item borderBottom>
                    <input class="nr h-[60rpx] w-full" :value="userInfo?.nickname" name="nickname" type="nickname"
                        placeholder="请输入昵称" />
                </u-form-item>
                <view class="mt-[80rpx]">
                    <button class="bg-primary text-white w-full h-[80rpx] !text-lg !leading-[80rpx] rounded-full"
                        form-type="submit" hover-class="none" size="mini">
                        确定
                    </button>
                </view>
            </form>
        </view>
    </u-popup>

    <!-- 手机号修改组件 -->
    <u-popup v-model="showMobilePop" :closeable="true" mode="center" border-radius="20">
        <view class="px-[50rpx] py-[40rpx] bg-white" style="width: 85vw">
            <view class="mb-[70rpx] text-xl text-center">修改手机号码</view>
            <u-form-item borderBottom>
                <u-input class="flex-1" v-model="newMobile" placeholder="请输入新的手机号码" :border="false" />
            </u-form-item>
            <u-form-item borderBottom>
                <u-input class="flex-1" v-model="mobileCode" placeholder="请输入验证码" :border="false" />
                <view class="border-l border-solid border-0 border-light pl-3 text-muted leading-4 ml-3 w-[180rpx]"
                    @click="sendSms">
                    <u-verification-code ref="uCodeRef" :seconds="60" @change="codeChange" change-text="x秒" />
                    {{ codeTips }}
                </view>
            </u-form-item>
            <view class="mt-[80rpx]">
                <u-button @click="changeCodeMobile" type="primary" shape="circle"> 确定 </u-button>
            </view>
        </view>
    </u-popup>
</template>

<script lang="ts" setup>
import { ref, shallowRef } from 'vue'
import { onShow, onUnload } from '@dcloudio/uni-app'
import { userEdit } from '@/api/user'
import { smsSend } from '@/api/app'
import { FieldType } from '@/enums/appEnums'
import { uploadImage } from '@/api/app'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'

// 用户信息
const userInfo = ref<any>({})
// 用户信息的枚举
const fieldType = ref(FieldType.NONE)
//显示昵称弹窗
const showNickName = ref<boolean | null>(false)
// 显示手机号验证码调整弹窗 非小程序才需要
const showMobilePop = ref<boolean | null>(false)

//新昵称
const newNickname = ref<string>('')
//新的手机号码
const newMobile = ref<string>('')

//修改手机验证码
const mobileCode = ref<string>('')
const codeTips = ref('')
const uCodeRef = shallowRef()

// 获取用户信息
const getUser = async (): Promise<void> => {
    const userStore = useUserStore()
    const { userInfo: storeUserInfo } = storeToRefs(userStore)
    userInfo.value = storeUserInfo.value
}

// 获取验证码显示字段
const codeChange = (text: string) => {
    codeTips.value = text
}

// 发送验证码
const sendSms = async () => {
    if (!newMobile.value) return uni.$u.toast('请输入新的手机号码')
    if (uCodeRef.value?.canGetCode) {
        await smsSend(newMobile.value)
        uni.$u.toast('发送成功')
        uCodeRef.value?.start()
    }
}

// 验证码修改手机号-非微信小程序
const changeCodeMobile = async () => {
    await setUserInfoFun({ phone: newMobile.value, mobileCode: mobileCode.value })
    showMobilePop.value = false
    getUser()
}

// 修改用户信息
const setUserInfoFun = async (data: any): Promise<void> => {
    const { code, msg } = await userEdit(
        Object.assign(data, {
            userId: userInfo.value?.userId,
            username: userInfo.value?.username
        })
    )

    if (code === 1) {
        uni.$u.toast(msg)
    } else {
        uni.$u.toast('操作成功')
        getUser()
    }
}

// 上传头像
const chooseAvatar = (e: any) => {
    fieldType.value = FieldType.AVATAR
    // #ifndef MP-WEIXIN
    uni.navigateTo({
        url: '/uni_modules/vk-uview-ui/components/u-avatar-cropper/u-avatar-cropper?destWidth=300&rectWidth=200&fileType=jpg'
    })
    // #endif
    // #ifdef MP-WEIXIN
    if (e.detail.avatarUrl) {
        uploadAvatar(e.detail.avatarUrl)
    }
    // #endif
}

// 修改用户昵称
const changeNameConfirm = async (e: any) => {
    newNickname.value = e.detail.value.nickname
    if (newNickname.value == '') return uni.$u.toast('昵称不能为空')
    if (newNickname.value.length > 10) return uni.$u.toast('昵称长度不得超过十位数')
    fieldType.value = FieldType.NICKNAME
    await setUserInfoFun({ nickname: newNickname.value })

    showNickName.value = false
}

const uploadAvatar = (path: string) => {
    uni.showLoading({
        title: '正在上传中...',
        mask: true
    })
    uploadImage(path)
        .then((res: { data?: { url: string } }) => {
            uni.hideLoading()
            setUserInfoFun({ avatar: res.data?.url })
        })
        .catch(() => {
            uni.hideLoading()
            uni.$u.toast('上传失败')
        })
}

const { getImageUrl } = useAppStore()

// 监听从裁剪页发布的事件，获得裁剪结果
uni.$on('uAvatarCropper', (path) => {
    uploadAvatar(path)
})

onShow(async () => {
    getUser()
})

onUnload(() => {
    uni.$off('uAvatarCropper')
})
</script>

<style lang="scss">
.header {
    width: 100%;

    image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
    }
}

.item {
    margin-top: 2rpx;
    padding: 30rpx;
    background-color: #ffffff;

    .label {
        width: 150rpx;
    }

    .content {
        flex: 1;
        width: 80%;
    }
}
</style>
