import { getUserCenter, getUserTocCenter } from "@/api/user";
import { TOKEN_KEY } from "@/enums/cacheEnums";
import cache from "@/utils/cache";
import { defineStore } from "pinia";
const isToc = import.meta.env.VITE_IS_TOC === "true";

interface UserSate {
  userInfo: Record<string, any>;
  token: string | null;
  temToken: string | null;
}
export const useUserStore = defineStore({
  id: "userStore",
  state: (): UserSate => ({
    userInfo: {},
    token: cache.get(TOKEN_KEY) || null,
    temToken: null,
  }),
  getters: {
    isLogin: (state) => !!state.token,
  },
  actions: {
    async getUser() {
      if (isToc) {
        const { data } = await getUserTocCenter();
        this.userInfo = data.appUser;
      } else {
        const { data } = await getUserCenter();
        this.userInfo = data.sysUser;
      }
    },
    login(token: string) {
      this.token = token;
      cache.set(TOKEN_KEY, token);
    },
    logout() {
      this.token = "";
      this.userInfo = {};
      cache.remove(TOKEN_KEY);
    },
  },
});
