import request from "@/utils/request";

//首页数据
export function getIndex(): Promise<any> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockIndexData);
    }, 500);
  });
}

// 装修页面
export function getDecorate(data: any) {
  return request.get({ url: "/app/index/decorate", data });
}

// 如果 http 不存在，请注释掉或删除以下代码
// import { http } from '@/utils/http';

// export function getBanners() {
//     return http.get('/api/shop/banners');
// }

// export function getCategories() {
//     return http.get('/api/shop/categories');
// }

// export function getHotProducts() {
//     return http.get('/api/shop/hot-products');
// }

interface Banner {
  image: string;
}

interface Category {
  id: number;
  name: string;
  icon: string;
}

interface Product {
  id: number;
  name: string;
  price: number;
  image: string;
  description: string;
}

interface IndexData {
  banners: Banner[];
  categories: Category[];
  hotProducts: Product[];
}

const mockIndexData: IndexData = {
  banners: [
    { image: "/static/images/banner1.jpg" },
    { image: "/static/images/banner2.jpg" },
    { image: "/static/images/banner3.jpg" },
  ],
  categories: [
    { id: 1, name: "手机", icon: "/static/images/category/phone.png" },
    { id: 2, name: "电脑", icon: "/static/images/category/laptop.png" },
    { id: 3, name: "配件", icon: "/static/images/category/accessory.png" },
    { id: 4, name: "更多", icon: "/static/images/category/more.png" },
  ],
  hotProducts: [
    {
      id: 1,
      name: "新款智能手机",
      price: 2999,
      image: "/static/images/products/phone1.jpg",
      description: "最新款智能手机，性能强劲",
    },
    {
      id: 2,
      name: "高性能笔记本",
      price: 5999,
      image: "/static/images/products/laptop1.jpg",
      description: "轻薄便携，续航持久",
    },
    {
      id: 3,
      name: "无线蓝牙耳机",
      price: 299,
      image: "/static/images/products/headphone1.jpg",
      description: "高音质，舒适佩戴",
    },
    {
      id: 4,
      name: "智能手表",
      price: 1299,
      image: "/static/images/products/watch1.jpg",
      description: "多功能智能手表，健康监测",
    },
  ],
};

export interface ProductDetail {
  id: number;
  name: string;
  price: number;
  image: string;
  images: string[];
  description: string;
  detailHtml: string;
  specifications: { name: string; value: string }[];
}

const mockProductDetails: Record<number, ProductDetail> = {
  1: {
    id: 1,
    name: "新款智能手机 Pro Max",
    price: 5999,
    image: "/static/images/products/phone1.jpg",
    images: [
      "/static/images/products/phone1.jpg",
      "/static/images/products/phone1_detail1.jpg",
      "/static/images/products/phone1_detail2.jpg",
    ],
    description: "最新旗舰智能手机，搭载顶级处理器和先进摄像系统",
    detailHtml: `
      <h2>产品特点</h2>
      <ul>
        <li>6.7英寸视网��XDR显示屏</li>
        <li>A16仿生芯片，性能强劲</li>
        <li>专业级三摄系统，支持8K视频录制</li>
        <li>Face ID面部识别，安全便捷</li>
        <li>5G网络支持，畅享高速互联</li>
      </ul>
      <p>这款智能手机代表了移动科技的巅峰之作，无论是日常使用还是专业需求，都能完美胜任。</p>
    `,
    specifications: [
      { name: "屏幕尺寸", value: "6.7英寸" },
      { name: "处理器", value: "A16仿生芯片" },
      { name: "存储容量", value: "128GB/256GB/512GB/1TB" },
      { name: "主摄像头", value: "48MP + 12MP + 12MP" },
      { name: "置像头", value: "12MP" },
      { name: "电池容量", value: "4400mAh" },
      { name: "操作系统", value: "iOS 16" },
    ],
  },
  // ... 可以添加更多商品的详细信息
};

export function getProductDetail(id: number): Promise<ProductDetail> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockProductDetails[id] || mockProductDetails[1]); // 如果找不到对应ID的商品，返回第一个商品的详情
    }, 500);
  });
}

export interface CategoryProduct extends Product {
  categoryId: number;
}

const mockCategoryProducts: Record<number, CategoryProduct[]> = {
  1: [
    { id: 101, name: "iPhone 13", price: 5999, image: "/static/images/products/iphone13.jpg", description: "最新款iPhone", categoryId: 1 },
    { id: 102, name: "Samsung Galaxy S21", price: 4999, image: "/static/images/products/galaxys21.jpg", description: "三星旗舰手机", categoryId: 1 },
    // 添加更多手机产品...
  ],
  2: [
    { id: 201, name: "MacBook Pro", price: 11999, image: "/static/images/products/macbookpro.jpg", description: "专业级笔记本电脑", categoryId: 2 },
    { id: 202, name: "Dell XPS 13", price: 8999, image: "/static/images/products/dellxps13.jpg", description: "轻薄高性能笔记本", categoryId: 2 },
    // 添加更多脑产品...
  ],
  // 添加更多分类的产品...
};

export function getCategoryProducts(categoryId: number): Promise<CategoryProduct[]> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockCategoryProducts[categoryId] || []);
    }, 500);
  });
}

// 在文件顶部添加这行
export type { Product };

// 添加购物车相关的接口和函数
export interface CartItem extends Product {
  quantity: number;
}

let mockCartItems: CartItem[] = [];

export function getCartItems(): Promise<CartItem[]> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockCartItems);
    }, 200);
  });
}

export function addToCart(product: Product, quantity: number = 1): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const existingItem = mockCartItems.find(item => item.id === product.id);
      if (existingItem) {
        existingItem.quantity += quantity;
      } else {
        mockCartItems.push({ ...product, quantity });
      }
      resolve();
    }, 200);
  });
}

// 修改 updateCartItemQuantity 函数
export function updateCartItemQuantity(productId: number, quantity: number): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockCartItems.find(item => item.id === productId);
      if (item) {
        item.quantity = quantity;
      }
      resolve();
    }, 200);
  });
}

// 添加批量更新购物车项目的函数
export function updateCartItems(items: CartItem[]): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      mockCartItems = items;
      resolve();
    }, 200);
  });
}

export function removeFromCart(productId: number): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      mockCartItems = mockCartItems.filter(item => item.id !== productId);
      resolve();
    }, 200);
  });
}

// 添加地址相关的接口和函数
export interface Address {
  id: number;
  name: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detail: string;
}

let mockAddresses: Address[] = [
  {
    id: 1,
    name: '张三',
    phone: '13800138000',
    province: '广东省',
    city: '深圳市',
    district: '南山区',
    detail: '科技园 1 号楼 101 室'
  },
  {
    id: 2,
    name: '李四',
    phone: '13900139000',
    province: '北京市',
    city: '北京市',
    district: '朝阳区',
    detail: '三里屯 SOHO 2 号楼 303 室'
  }
];

export function getAddresses(): Promise<Address[]> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockAddresses);
    }, 200);
  });
}

export function deleteAddress(id: number): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      mockAddresses = mockAddresses.filter(address => address.id !== id);
      resolve();
    }, 200);
  });
}

// 添加订单相关的接口和函数
export interface Order {
  id: number;
  orderNumber: string;
  status: string;
  products: (Product & { quantity: number })[];
  total: number;
}

let mockOrders: Order[] = [
  {
    id: 1,
    orderNumber: 'ORD20230001',
    status: '已完成',
    products: [
      { ...mockIndexData.hotProducts[0], quantity: 1 },
      { ...mockIndexData.hotProducts[1], quantity: 2 }
    ],
    total: mockIndexData.hotProducts[0].price + mockIndexData.hotProducts[1].price * 2
  },
  {
    id: 2,
    orderNumber: 'ORD20230002',
    status: '待发货',
    products: [
      { ...mockIndexData.hotProducts[2], quantity: 1 }
    ],
    total: mockIndexData.hotProducts[2].price
  }
];

export function getOrders(): Promise<Order[]> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockOrders);
    }, 200);
  });
}

// 在文件顶部添加这些导出
export type { Address, Order };
