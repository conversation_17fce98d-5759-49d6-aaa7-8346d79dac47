<template>
  <view class="flex flex-col h-screen bg-gray-100">
    <!-- 搜索栏 -->
    <view class="p-4 bg-white shadow-md">
      <u-search
        placeholder="搜索商品"
        v-model="searchKeyword"
        @search="onSearch"
        class="bg-gray-200 rounded-full"
      />
    </view>

    <!-- 主要内容区域 -->
    <scroll-view scroll-y class="flex-1 overflow-y-auto">
      <!-- 轮播图 -->
      <swiper class="h-40 mb-4" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000">
        <swiper-item v-for="(banner, index) in banners" :key="index">
          <image :src="banner.image" mode="aspectFill" class="w-full h-full"/>
        </swiper-item>
      </swiper>

      <!-- 商品分类 -->
      <view class="bg-white p-4 mb-4">
        <view class="text-lg font-bold mb-2">商品分类</view>
        <view class="grid grid-cols-4 gap-4">
          <view v-for="category in categories" :key="category.id" class="flex flex-col items-center">
            <image :src="category.icon" mode="aspectFit" class="w-12 h-12 mb-1"/>
            <text class="text-sm">{{ category.name }}</text>
          </view>
        </view>
      </view>

      <!-- 热门商品 -->
      <view class="bg-white p-4">
        <view class="text-lg font-bold mb-2">热门商品</view>
        <view class="grid grid-cols-2 gap-4">
          <view v-for="product in hotProducts" :key="product.id" class="bg-gray-50 rounded-lg overflow-hidden shadow">
            <image :src="product.image" mode="aspectFill" class="w-full h-32 object-cover"/>
            <view class="p-2">
              <text class="text-sm font-medium">{{ product.name }}</text>
              <view class="mt-1 flex justify-between items-center">
                <text class="text-red-500 font-bold">¥{{ product.price }}</text>
                <u-button size="mini" type="primary">购买</u-button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

const searchKeyword = ref('');
const banners = ref([
  { image: '/static/images/banner1.jpg' },
  { image: '/static/images/banner2.jpg' },
  { image: '/static/images/banner3.jpg' },
]);
const categories = ref([
  { id: 1, name: '手机', icon: '/static/images/category/phone.png' },
  { id: 2, name: '电脑', icon: '/static/images/category/laptop.png' },
  { id: 3, name: '配件', icon: '/static/images/category/accessory.png' },
  { id: 4, name: '更多', icon: '/static/images/category/more.png' },
]);
const hotProducts = ref([
  { id: 1, name: '新款智能手机', price: 2999, image: '/static/images/products/phone1.jpg' },
  { id: 2, name: '高性能笔记本', price: 5999, image: '/static/images/products/laptop1.jpg' },
  { id: 3, name: '无线蓝牙耳机', price: 299, image: '/static/images/products/headphone1.jpg' },
  { id: 4, name: '智能手表', price: 1299, image: '/static/images/products/watch1.jpg' },
]);

const onSearch = () => {
  console.log('Searching for:', searchKeyword.value);
  // 实现搜索逻辑
};

onMounted(() => {
  console.log('Shop homepage mounted');
  // 这里可以添加获取数据的逻辑，例如从API获取轮播图、分类和热门商品数据
});
</script>