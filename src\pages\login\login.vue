<template>
    <view class="bg-white login min-h-full flex flex-col items-center px-[40rpx] pt-[80rpx] box-border">
        <view class="fixed flex justify-end right-4 top-4">
            <u-icon name="setting" class="text-primary" :size="48" @click="showTenantList = true"></u-icon>
            <u-action-sheet :list="tenantList" title="租户列表" v-model="showTenantList" :safe-area-inset-bottom="true"
                @click="handleClick"></u-action-sheet>
        </view>
        <view class="user-info flex px-[50rpx] justify-between py-[50rpx]">
            <u-image src="@/static/images/logo/logo.png" height="200" width="480" />
        </view>
        <view class="mt-4 text-xl font-medium">{{ appStore.config.website?.name }}</view>
        <view class="w-full mt-[60rpx] pb-[60rpx]">
            <!-- #ifndef MP-WEIXIN -->
            <u-form borderBottom>
                <template v-if="loginWay == LoginWayEnum.ACCOUNT">
                    <u-form-item borderBottom>
                        <u-icon class="mr-2" :size="36" name="/static/images/icon/icon_user.png" />
                        <u-input class="flex-1" v-model="formData.username" :border="false" placeholder="请输入手机号/账号" />
                    </u-form-item>
                    <u-form-item borderBottom>
                        <u-icon class="mr-2" :size="36" name="/static/images/icon/icon_password.png" />
                        <u-input class="flex-1" v-model="formData.password" type="password" placeholder="请输入密码"
                            :border="false" />
                    </u-form-item>
                </template>
                <template v-if="loginWay == LoginWayEnum.MOBILE">
                    <u-form-item borderBottom>
                        <u-icon class="mr-2" :size="36" name="/static/images/icon/icon_mobile.png" />
                        <u-input class="flex-1" v-model="formData.mobile" :border="false" placeholder="请输入手机号码" />
                    </u-form-item>
                    <u-form-item borderBottom>
                        <u-icon class="mr-2" :size="36" name="/static/images/icon/icon_code.png" />
                        <u-input class="flex-1" v-model="formData.code" placeholder="请输入验证码" :border="false" />
                        <view class="border-l border-solid border-0 border-light pl-3 leading-4 ml-3 w-[180rpx]"
                            @click="sendSms">
                            <u-verification-code ref="uCodeRef" :seconds="60" @change="codeChange" change-text="x秒" />
                            <text :class="formData.mobile ? 'text-primary' : 'text-muted'">
                                {{ codeTips }}
                            </text>
                        </view>
                    </u-form-item>
                </template>
            </u-form>
            <view class="mt-[60rpx]">
                <u-button type="primary" shape="circle" @click="handleLogin(formData.scene)">
                    登 录
                </u-button>
            </view>

            <view class="text-content flex justify-between mt-[40rpx]">
                <view class="flex-1">
                    <view v-if="loginWay == LoginWayEnum.MOBILE"
                        @click="changeLoginWay(LoginTypeEnum.ACCOUNT, LoginWayEnum.ACCOUNT)">
                        账号密码登录
                    </view>
                    <view v-if="loginWay == LoginWayEnum.ACCOUNT"
                        @click="changeLoginWay(LoginTypeEnum.MOBILE, LoginWayEnum.MOBILE)">
                        短信验证码登录
                    </view>
                </view>

                <navigator url="/pages/register/register" hover-class="none"
                    class="text-sm transition-colors duration-300 text-primary hover:text-primary-dark">
                    注册账号
                </navigator>
            </view>
            <!-- #endif -->

            <!-- #ifdef MP-WEIXIN || H5 -->
            <view class="mt-[80rpx]" v-if="isWeixin">
                <u-divider bgColor="none">一键登录</u-divider>
                <div class="flex justify-center mt-[40rpx]">
                    <div v-if="isWeixin" class="flex flex-col items-center" @click="wxLogin">
                        <img src="@/static/images/icon/icon_wx.png" class="w-[80rpx] h-[80rpx]" />
                    </div>
                </div>
            </view>
            <!-- #endif -->
        </view>
        <view class="mt-[40rpx]">
            <u-checkbox v-model="isCheckAgreement" shape="circle">
                <view class="flex text-xs">
                    已阅读并同意
                    <view @click.stop>
                        <navigator class="text-primary" hover-class="none"
                            url="/pages/agreement/agreement?type=service">
                            《服务协议》
                        </navigator>
                    </view>

                    和
                    <view @click.stop>
                        <navigator class="text-primary" hover-class="none"
                            url="/pages/agreement/agreement?type=privacy">
                            《隐私协议》
                        </navigator>
                    </view>
                </view>
            </u-checkbox>
        </view>
        <mplogin-popup v-model:show="showLoginPopup" :logo="websiteConfig.logo" :title="websiteConfig.name"
            @update="handleUpdateUser" />
    </view>
</template>

<script setup lang="ts">
import { mobileLogin, accountLogin, mnpLogin, OALogin } from '@/api/account'
import { smsSend, queryTenantList } from '@/api/app'
import { getUserCenter, userEdit } from '@/api/user'
import { BACK_URL } from '@/enums/cacheEnums'
import { useLockFn } from '@/hooks/useLockFn'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import cache from '@/utils/cache'
import wechatOa from '@/utils/wechat'
import { isWeixinClient } from '@/utils/client'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { computed, reactive, ref, shallowRef } from 'vue'
enum LoginTypeEnum {
    MOBILE = 'mobile',
    ACCOUNT = 'account'
}

enum LoginWayEnum {
    ACCOUNT = 1,
    MOBILE = 2
}

const isWeixin = ref(true)
const showLoginPopup = ref(false)
// #ifdef H5
isWeixin.value = isWeixinClient()
// #endif

const userStore = useUserStore()
const appStore = useAppStore()

const websiteConfig = computed(() => appStore.getWebsiteConfig)
const uCodeRef = shallowRef()
const loginWay = ref<LoginWayEnum>(LoginWayEnum.ACCOUNT)
const codeTips = ref('')
const isCheckAgreement = ref(false)
const loginData = ref<any>({})
const showTenantList = ref(false)
const formData = reactive({
    scene: '',
    username: '',
    password: '',
    code: '',
    mobile: ''
})

const tenantList = ref([])

const getTenantList = async () => {
  const { data } = await queryTenantList()

  const currentDomain = getCurrentDomain()

  tenantList.value = data.map((item: any) => {
    console.log(cache.getTenant(), item.id)

    console.log(currentDomain, item.tenantDomain)

    if (item.tenantDomain === currentDomain) {
      console.log('设置租户', item.id)
      cache.set('tenantId', item.id)
    }
    return {
      text: item.name,
      id: item.id,
      subText: cache.getTenant() === item.id ? '当前' : ''
    }
  })
}

// 根据域名获取租户ID
const getCurrentDomain = () => {
  // #ifdef H5
  return window.location.hostname
  // #endif
  // #ifndef H5
  return '' // 非H5环境根据实际情况处理
  // #endif
}

// 修改/忘记密码
const handleClick = (index: number) => {
    const tenant = tenantList.value[index]
    cache.set('tenantId', tenant.id)
}

const codeChange = (text: string) => {
    codeTips.value = text
}

const sendSms = async () => {
    if (!formData.mobile || formData.mobile.length !== 11) {
        uni.$u.toast('手机号不合法')
        return
    }
    if (uCodeRef.value?.canGetCode) {
        await smsSend(formData.mobile)
        uni.$u.toast('发送成功')
        uCodeRef.value?.start()
    }
}

const changeLoginWay = (type: LoginTypeEnum, way: LoginWayEnum) => {
    formData.scene = type
    loginWay.value = way
}

const loginFun = async (scene: LoginTypeEnum) => {
    try {
        await checkAgreement()
        if (scene == LoginTypeEnum.ACCOUNT) {
            if (!formData.username) return uni.$u.toast('请输入账号/手机号码')
            if (!formData.password) return uni.$u.toast('请输入密码')
        }
        if (scene == LoginTypeEnum.MOBILE) {
            if (!formData.mobile) return uni.$u.toast('请输入手机号码')
            if (!formData.code) return uni.$u.toast('请输入验证码')
        }
        uni.showLoading({
            title: '请稍后...'
        })

        let data
        switch (scene) {
            case LoginTypeEnum.MOBILE:
                data = await mobileLogin(formData)
                break
            default:
                data = await accountLogin(formData)
                break
        }

        if (data) {
            loginHandle(data)
        }
    } catch (error: any) {
        uni.hideLoading()
        uni.$u.toast(error)
    }
}

const loginHandle = async (data: any) => {
    const { access_token } = data
    userStore.login(access_token)
    await userStore.getUser()
    uni.$u.toast('登录成功')
    uni.hideLoading()
    uni.reLaunch({
        url: '/pages/index/index'
    })
}

const { lockFn: handleLogin } = useLockFn(loginFun)

const checkAgreement = async () => {
    if (!isCheckAgreement.value)
        return Promise.reject('请勾选底部已阅读并同意《服务协议》和《隐私协议》')
}
const { lockFn: wxLogin } = useLockFn(async () => {
    try {
        await checkAgreement()
        // #ifdef MP-WEIXIN
        uni.showLoading({
            title: '请稍后...'
        })
        const { code }: any = await uni.login({
            provider: 'weixin'
        })
        const data = await mnpLogin(code)
        loginData.value = data
        const { access_token } = data

        // 临时用户没有昵称
        userStore.login(access_token)
        const res = await getUserCenter()
        if (!res.data.appUser.nickname) {
            uni.hideLoading()
            userStore.temToken = access_token
            showLoginPopup.value = true
            return
        }
        loginHandle(data)
        // #endif
        // #ifdef H5
        if (isWeixin.value) {
          wechatOa.getUrl()
        }
        // #endif
    } catch (error) {
        uni.$u.toast(error)
    }
})

const handleUpdateUser = async (value: any) => {
    await userEdit(value)
    showLoginPopup.value = false
    loginHandle(loginData.value)
}

onShow(async () => {
    try {
        if (userStore.isLogin) {
            uni.showLoading({
                title: '请稍后...'
            })
            await userStore.getUser()
            uni.hideLoading()
            uni.navigateBack()
        }
    } catch (error: any) {
        uni.hideLoading()
    }
})

onLoad(async (options) => {
    // 清空用户信息
    userStore.logout()
    // 获取租户列表
    getTenantList()

    if (userStore.isLogin) {
        // 已经登录 => 首页
        uni.reLaunch({
            url: '/pages/index/index'
        })
        return
    }

    // #ifdef H5
    const { code, state } = options
    if (!isWeixin.value) return
    if (code) {
      const data = await OALogin(code)

      if (data) {
        loginHandle(data)
      }
    }
    // #endif
})
</script>

<style lang="scss">
page {
    height: 100%;
}
</style>
