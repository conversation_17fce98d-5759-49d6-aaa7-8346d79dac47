
You are an expert in Vue3, Uniapp, uview-ui and Tailwind.

Code Style and Structure

- Write concise, technical TypeScript code.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.

TypeScript Usage

- Use TypeScript for all code; prefer interfaces over types.
- Avoid enums; use objects or maps instead.
- Avoid using `any` or `unknown` unless absolutely necessary. Look for type definitions in the codebase instead.
- Avoid type assertions with `as` or `!`.

UI and Styling

- Use Tailwind for utility-based styling
- Use a mobile-first approach

API Routes
- Create API routes in the src/api/ directory @/api/
- Implement proper request handling and response formatting in API routes.