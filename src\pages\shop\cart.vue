<template>
    <view class="cart-container">
        <view v-if="cartItems.length > 0">
            <!-- 商品列表 -->
            <view v-for="item in cartItems" :key="item.id" class="cart-item">
                <view class="flex items-center">
                    <checkbox :checked="item.selected" @change="toggleSelect(item)" />
                    <image :src="item.image" mode="aspectFit" class="cart-item-image" />
                </view>
                <view class="cart-item-info">
                    <text class="cart-item-name">{{ item.name }}</text>
                    <text class="cart-item-price">¥{{ item.price }}</text>
                    <view class="cart-item-actions">
                        <u-number-box v-model="item.quantity" @change="updateQuantity(item)" :min="1" :max="99" />
                    </view>
                </view>
            </view>

            <!-- 底部结算栏 -->
            <view class="cart-footer">
                <view class="flex items-center">
                    <checkbox :checked="allSelected" @change="toggleSelectAll">全选</checkbox>
                    <text class="ml-4">合计: <text class="font-bold text-red-500">¥{{ totalPrice }}</text></text>
                </view>
                <u-button type="primary" @click="checkout" :disabled="!hasSelectedItems">结算({{ selectedCount
                    }})</u-button>
            </view>
        </view>
        <view v-else class="cart-empty">
            <u-icon name="shopping-cart" size="64" color="#999"></u-icon>
            <text class="mt-4">购物车还是空的</text>
            <u-button type="primary" @click="goShopping" class="mt-4">去逛逛</u-button>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getCartItems, updateCartItemQuantity, removeFromCart, type CartItem } from '@/api/shop'

const cartItems = ref<(CartItem & { selected: boolean })[]>([])

const refreshCartItems = async () => {
    const items = await getCartItems()
    cartItems.value = items.map((item) => ({ ...item, selected: true }))
}

onMounted(refreshCartItems)

const allSelected = computed(() => cartItems.value.every((item) => item.selected))
const hasSelectedItems = computed(() => cartItems.value.some((item) => item.selected))
const selectedCount = computed(() => cartItems.value.filter((item) => item.selected).length)

const totalPrice = computed(() => {
    return cartItems.value
        .filter((item) => item.selected)
        .reduce((total, item) => total + item.price * item.quantity, 0)
        .toFixed(2)
})

const toggleSelect = (item: CartItem & { selected: boolean }) => {
    item.selected = !item.selected
}

const toggleSelectAll = () => {
    const newState = !allSelected.value
    cartItems.value.forEach((item) => (item.selected = newState))
}

const updateQuantity = async (item: CartItem) => {
    await updateCartItemQuantity(item.id, item.quantity)
}

const removeItem = async (item: CartItem) => {
    await removeFromCart(item.id)
    await refreshCartItems()
}

const checkout = () => {
    const selectedItems = cartItems.value.filter((item) => item.selected)
    // 实现结算逻辑
    uni.showToast({
        title: `结算 ${selectedItems.length} 件商品`,
        icon: 'none'
    })
}

const goShopping = () => {
    uni.switchTab({
        url: '/pages/shop/shop'
    })
}
</script>

<style scoped>
.cart-container {
    padding: 20px;
    padding-bottom: 100px;
    /* 为底部结算栏留出空间 */
}

.cart-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cart-item-image {
    width: 80px;
    height: 80px;
    margin-right: 10px;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-size: 16px;
    font-weight: bold;
}

.cart-item-price {
    color: #ff6b6b;
    font-size: 18px;
    margin-top: 4px;
}

.cart-item-actions {
    margin-top: 8px;
}

.cart-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #ffffff;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.cart-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: #999;
}
</style>
