<template>
    <view class="flex flex-col items-center flex-1 as-us">
        <image src="@/static/images/logo/logo.png" mode="" class="img"></image>
        <view class="text-content mt-[20rpx]">当前版本 {{ version }}</view>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const version = ref(import.meta.env.VITE_APP_VERSION)
</script>

<style lang="scss" scoped>
.as-us {
    .img {
        width: 160rpx;
        height: 160rpx;
        border-radius: 20rpx;
        margin-top: 96rpx;
    }
}
</style>
